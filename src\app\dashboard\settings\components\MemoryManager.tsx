'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Memory, MemoryCategory } from '@/lib/settingsService';
import memoryService from '@/lib/memoryService';
import { getUserConversations, Conversation } from '@/lib/conversationService';

interface MemoryManagerProps {
  selectedChatId?: string; // Para memórias específicas de chat
}

const BACKGROUND_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#8B5CF6', // violet-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#F97316', // orange-500
  '#EC4899', // pink-500
  '#6B7280', // gray-500
];

export default function MemoryManager({ selectedChatId }: MemoryManagerProps) {
  const { user } = useAuth();
  const [memories, setMemories] = useState<Memory[]>([]);
  const [categories, setCategories] = useState<MemoryCategory[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showMemoryModal, setShowMemoryModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingMemory, setEditingMemory] = useState<Memory | null>(null);
  const [editingCategory, setEditingCategory] = useState<MemoryCategory | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Form states
  const [memoryForm, setMemoryForm] = useState({
    title: '',
    content: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true,
    categoryId: '',
    chatId: selectedChatId || ''
  });

  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [memoriesData, categoriesData, conversationsData] = await Promise.all([
        memoryService.getMemories(user.uid),
        memoryService.getCategories(user.uid),
        getUserConversations(user.uid)
      ]);

      setMemories(memoriesData);
      setCategories(categoriesData);
      setConversations(conversationsData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMemory = async () => {
    if (!user || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const memoryData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        memoryData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        memoryData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.createMemory(user.uid, memoryData);

      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar memória:', error);
    }
  };

  const handleUpdateMemory = async () => {
    if (!user || !editingMemory || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const updateData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        updateData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        updateData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.updateMemory(user.uid, editingMemory.id, updateData);

      setEditingMemory(null);
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar memória:', error);
    }
  };

  const handleDeleteMemory = async (memoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta memória?')) return;

    try {
      await memoryService.deleteMemory(user.uid, memoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar memória:', error);
    }
  };

  const handleCreateCategory = async () => {
    if (!user || !categoryForm.name.trim()) return;

    try {
      const categoryData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        categoryData.description = categoryForm.description.trim();
      }

      await memoryService.createCategory(user.uid, categoryData);

      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
    }
  };

  const handleUpdateCategory = async () => {
    if (!user || !editingCategory || !categoryForm.name.trim()) return;

    try {
      const updateData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        updateData.description = categoryForm.description.trim();
      }

      await memoryService.updateCategory(user.uid, editingCategory.id, updateData);

      setEditingCategory(null);
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta categoria e todas as suas memórias?')) return;

    try {
      await memoryService.deleteCategory(user.uid, categoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar categoria:', error);
    }
  };

  const handleToggleCategoryMemories = async (categoryId: string, isActive: boolean) => {
    if (!user) return;

    try {
      await memoryService.toggleCategoryMemories(user.uid, categoryId, isActive);
      await loadData();
    } catch (error) {
      console.error('Erro ao alternar memórias da categoria:', error);
    }
  };

  const openMemoryModal = (memory?: Memory) => {
    if (memory) {
      setEditingMemory(memory);
      setMemoryForm({
        title: memory.title,
        content: memory.content,
        backgroundColor: memory.backgroundColor,
        isActive: memory.isActive,
        categoryId: memory.categoryId || '',
        chatId: memory.chatId || ''
      });
    } else {
      setEditingMemory(null);
      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
    }
    setShowMemoryModal(true);
  };

  const openCategoryModal = (category?: MemoryCategory) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        name: category.name,
        description: category.description || '',
        backgroundColor: category.backgroundColor,
        isActive: category.isActive
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
    }
    setShowCategoryModal(true);
  };

  const filteredMemories = memories.filter(memory => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'uncategorized') return !memory.categoryId;
    return memory.categoryId === selectedCategory;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com botões de ação */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-white">Gerenciar Memórias</h3>
          <p className="text-blue-300/70 text-sm">
            {selectedChatId ? 'Memórias específicas para este chat' : 'Memórias globais e organizadas por categorias'}
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => openCategoryModal()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Nova Categoria
          </button>
          <button
            onClick={() => openMemoryModal()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Nova Memória
          </button>
        </div>
      </div>

      {/* Modal de Memória */}
      {showMemoryModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-blue-950 border border-blue-700/30 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-white">
                {editingMemory ? 'Editar Memória' : 'Nova Memória'}
              </h3>
              <button
                onClick={() => setShowMemoryModal(false)}
                className="text-blue-300 hover:text-blue-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Título */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Título *
                </label>
                <input
                  type="text"
                  value={memoryForm.title}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências do usuário"
                />
              </div>

              {/* Conteúdo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Conteúdo *
                </label>
                <textarea
                  value={memoryForm.content}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, content: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Ex: O usuário prefere respostas diretas e objetivas..."
                />
              </div>

              {/* Categoria */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Categoria
                </label>
                <select
                  value={memoryForm.categoryId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, categoryId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Sem categoria</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Chat específico */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Chat específico
                </label>
                <select
                  value={memoryForm.chatId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, chatId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Memória global (todos os chats)</option>
                  {conversations.map(conversation => (
                    <option key={conversation.id} value={conversation.id}>
                      {conversation.name}
                    </option>
                  ))}
                </select>
                <p className="text-blue-400/70 text-xs mt-1">
                  Se selecionado, esta memória só será usada no chat especificado
                </p>
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setMemoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        memoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="memory-active"
                  checked={memoryForm.isActive}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="memory-active" className="text-sm text-blue-300">
                  Memória ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowMemoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingMemory ? handleUpdateMemory : handleCreateMemory}
                disabled={!memoryForm.title.trim() || !memoryForm.content.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingMemory ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Categoria */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-blue-950 border border-blue-700/30 rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-white">
                {editingCategory ? 'Editar Categoria' : 'Nova Categoria'}
              </h3>
              <button
                onClick={() => setShowCategoryModal(false)}
                className="text-blue-300 hover:text-blue-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Nome *
                </label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências pessoais"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Descrição
                </label>
                <textarea
                  value={categoryForm.description}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Descrição opcional da categoria"
                />
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setCategoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        categoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="category-active"
                  checked={categoryForm.isActive}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="category-active" className="text-sm text-blue-300">
                  Categoria ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowCategoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingCategory ? handleUpdateCategory : handleCreateCategory}
                disabled={!categoryForm.name.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingCategory ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filtro por categoria */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setSelectedCategory('all')}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            selectedCategory === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40'
          }`}
        >
          Todas
        </button>
        <button
          onClick={() => setSelectedCategory('uncategorized')}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            selectedCategory === 'uncategorized'
              ? 'bg-blue-600 text-white'
              : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40'
          }`}
        >
          Sem categoria
        </button>
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-3 py-1 rounded-full text-sm transition-colors flex items-center gap-2 ${
              selectedCategory === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40'
            }`}
          >
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: category.backgroundColor }}
            />
            {category.name}
          </button>
        ))}
      </div>

      {/* Lista de categorias */}
      {categories.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-md font-medium text-white">Categorias</h4>
          <div className="grid gap-4">
            {categories.map(category => (
              <div 
                key={category.id} 
                className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4"
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: category.backgroundColor }}
                    />
                    <div>
                      <h5 className="font-medium text-white">{category.name}</h5>
                      {category.description && (
                        <p className="text-blue-300/70 text-sm">{category.description}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleToggleCategoryMemories(category.id, !category.isActive)}
                      className={`px-3 py-1 rounded text-sm transition-colors ${
                        category.isActive
                          ? 'bg-green-600 text-white hover:bg-green-700'
                          : 'bg-gray-600 text-gray-300 hover:bg-gray-700'
                      }`}
                    >
                      {category.isActive ? 'Ativo' : 'Inativo'}
                    </button>
                    <button
                      onClick={() => openCategoryModal(category)}
                      className="p-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDeleteCategory(category.id)}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Lista de memórias */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-white">
          Memórias {selectedCategory !== 'all' && `(${filteredMemories.length})`}
        </h4>
        {filteredMemories.length === 0 ? (
          <div className="text-center py-8 text-blue-300/70">
            <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Nenhuma memória encontrada</p>
            <p className="text-sm">Clique em "Nova Memória" para começar</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredMemories.map(memory => {
              const category = categories.find(c => c.id === memory.categoryId);
              const chat = conversations.find(c => c.id === memory.chatId);
              return (
                <div
                  key={memory.id}
                  className="border border-blue-700/30 rounded-lg p-4"
                  style={{
                    backgroundColor: `${memory.backgroundColor}15`,
                    borderColor: `${memory.backgroundColor}40`
                  }}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: memory.backgroundColor }}
                        />
                        <h5 className="font-medium text-white">{memory.title}</h5>
                        {memory.chatId && chat && (
                          <span className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded">
                            {chat.name}
                          </span>
                        )}
                        {memory.chatId && !chat && (
                          <span className="px-2 py-1 bg-red-600/20 text-red-300 text-xs rounded">
                            Chat não encontrado
                          </span>
                        )}
                        {category && (
                          <span className="px-2 py-1 bg-blue-600/20 text-blue-300 text-xs rounded">
                            {category.name}
                          </span>
                        )}
                      </div>
                      <p className="text-blue-200/80 text-sm mb-2">{memory.content}</p>
                      <p className="text-blue-300/50 text-xs">
                        Criado em {new Date(memory.createdAt).toLocaleDateString('pt-BR')}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => memoryService.updateMemory(user!.uid, memory.id, { isActive: !memory.isActive }).then(loadData)}
                        className={`px-3 py-1 rounded text-sm transition-colors ${
                          memory.isActive
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-gray-600 text-gray-300 hover:bg-gray-700'
                        }`}
                      >
                        {memory.isActive ? 'Ativo' : 'Inativo'}
                      </button>
                      <button
                        onClick={() => openMemoryModal(memory)}
                        className="p-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDeleteMemory(memory.id)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
