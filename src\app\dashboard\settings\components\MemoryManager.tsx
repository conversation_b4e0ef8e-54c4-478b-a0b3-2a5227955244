'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Memory, MemoryCategory } from '@/lib/settingsService';
import memoryService from '@/lib/memoryService';
import { getUserConversations, Conversation } from '@/lib/conversationService';

interface MemoryManagerProps {
  selectedChatId?: string; // Para memórias específicas de chat
}

const BACKGROUND_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#8B5CF6', // violet-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#F97316', // orange-500
  '#EC4899', // pink-500
  '#6B7280', // gray-500
];

export default function MemoryManager({ selectedChatId }: MemoryManagerProps) {
  const { user } = useAuth();
  const [memories, setMemories] = useState<Memory[]>([]);
  const [categories, setCategories] = useState<MemoryCategory[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showMemoryModal, setShowMemoryModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingMemory, setEditingMemory] = useState<Memory | null>(null);
  const [editingCategory, setEditingCategory] = useState<MemoryCategory | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Form states
  const [memoryForm, setMemoryForm] = useState({
    title: '',
    content: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true,
    categoryId: '',
    chatId: selectedChatId || ''
  });

  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [memoriesData, categoriesData, conversationsData] = await Promise.all([
        memoryService.getMemories(user.uid),
        memoryService.getCategories(user.uid),
        getUserConversations(user.uid)
      ]);

      setMemories(memoriesData);
      setCategories(categoriesData);
      setConversations(conversationsData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMemory = async () => {
    if (!user || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const memoryData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        memoryData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        memoryData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.createMemory(user.uid, memoryData);

      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar memória:', error);
    }
  };

  const handleUpdateMemory = async () => {
    if (!user || !editingMemory || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const updateData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        updateData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        updateData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.updateMemory(user.uid, editingMemory.id, updateData);

      setEditingMemory(null);
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar memória:', error);
    }
  };

  const handleDeleteMemory = async (memoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta memória?')) return;

    try {
      await memoryService.deleteMemory(user.uid, memoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar memória:', error);
    }
  };

  const handleCreateCategory = async () => {
    if (!user || !categoryForm.name.trim()) return;

    try {
      const categoryData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        categoryData.description = categoryForm.description.trim();
      }

      await memoryService.createCategory(user.uid, categoryData);

      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
    }
  };

  const handleUpdateCategory = async () => {
    if (!user || !editingCategory || !categoryForm.name.trim()) return;

    try {
      const updateData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        updateData.description = categoryForm.description.trim();
      }

      await memoryService.updateCategory(user.uid, editingCategory.id, updateData);

      setEditingCategory(null);
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta categoria e todas as suas memórias?')) return;

    try {
      await memoryService.deleteCategory(user.uid, categoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar categoria:', error);
    }
  };

  const handleToggleCategoryMemories = async (categoryId: string, isActive: boolean) => {
    if (!user) return;

    try {
      await memoryService.toggleCategoryMemories(user.uid, categoryId, isActive);
      await loadData();
    } catch (error) {
      console.error('Erro ao alternar memórias da categoria:', error);
    }
  };

  const openMemoryModal = (memory?: Memory) => {
    if (memory) {
      setEditingMemory(memory);
      setMemoryForm({
        title: memory.title,
        content: memory.content,
        backgroundColor: memory.backgroundColor,
        isActive: memory.isActive,
        categoryId: memory.categoryId || '',
        chatId: memory.chatId || ''
      });
    } else {
      setEditingMemory(null);
      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
    }
    setShowMemoryModal(true);
  };

  const openCategoryModal = (category?: MemoryCategory) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        name: category.name,
        description: category.description || '',
        backgroundColor: category.backgroundColor,
        isActive: category.isActive
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
    }
    setShowCategoryModal(true);
  };

  const filteredMemories = memories.filter(memory => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'uncategorized') return !memory.categoryId;
    return memory.categoryId === selectedCategory;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header melhorado com estatísticas e gradientes */}
      <div className="relative">
        {/* Background com gradiente e efeito glass */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl backdrop-blur-sm border border-blue-500/20"></div>

        <div className="relative p-6 space-y-6">
          {/* Título principal com ícone e estatísticas */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-xl border border-purple-400/30">
                  <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
                    Gerenciar Memórias
                  </h3>
                  <p className="text-blue-300/80 text-sm font-medium">
                    {selectedChatId ? 'Memórias específicas para este chat' : 'Memórias globais e organizadas por categorias'}
                  </p>
                </div>
              </div>

              {/* Estatísticas rápidas */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-500/10 border border-blue-400/20 rounded-lg">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-blue-300 text-sm font-medium">{memories.length} memórias</span>
                </div>
                <div className="flex items-center gap-2 px-3 py-1.5 bg-purple-500/10 border border-purple-400/20 rounded-lg">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  <span className="text-purple-300 text-sm font-medium">{categories.length} categorias</span>
                </div>
                <div className="flex items-center gap-2 px-3 py-1.5 bg-green-500/10 border border-green-400/20 rounded-lg">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-300 text-sm font-medium">
                    {memories.filter(m => m.isActive).length} ativas
                  </span>
                </div>
              </div>
            </div>

            {/* Botões de ação melhorados */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => openCategoryModal()}
                className="group relative px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 flex items-center gap-3"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-purple-600/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                <svg className="relative w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span className="relative">Nova Categoria</span>
              </button>

              <button
                onClick={() => openMemoryModal()}
                className="group relative px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 flex items-center gap-3"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                <svg className="relative w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span className="relative">Nova Memória</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Memória melhorado */}
      {showMemoryModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
          <div className="relative w-full max-w-3xl max-h-[90vh] overflow-hidden">
            {/* Background com gradiente e glass effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-purple-950/95 backdrop-blur-xl rounded-2xl border border-blue-500/30 shadow-2xl"></div>

            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 rounded-2xl blur-xl"></div>

            <div className="relative overflow-y-auto max-h-[90vh] custom-scrollbar">
              <div className="p-8">
                {/* Header do modal */}
                <div className="flex justify-between items-center mb-8">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-400/30">
                      <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
                        {editingMemory ? 'Editar Memória' : 'Nova Memória'}
                      </h3>
                      <p className="text-blue-300/70 text-sm mt-1">
                        {editingMemory ? 'Modifique as informações da memória' : 'Crie uma nova memória para personalizar as respostas da IA'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowMemoryModal(false)}
                    className="p-2 text-blue-300 hover:text-blue-200 hover:bg-blue-500/20 rounded-xl transition-all duration-200 hover:scale-110"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

            <div className="space-y-4">
              {/* Título */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Título *
                </label>
                <input
                  type="text"
                  value={memoryForm.title}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências do usuário"
                />
              </div>

              {/* Conteúdo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Conteúdo *
                </label>
                <textarea
                  value={memoryForm.content}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, content: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Ex: O usuário prefere respostas diretas e objetivas..."
                />
              </div>

              {/* Categoria */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Categoria
                </label>
                <select
                  value={memoryForm.categoryId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, categoryId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Sem categoria</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Chat específico */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Chat específico
                </label>
                <select
                  value={memoryForm.chatId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, chatId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Memória global (todos os chats)</option>
                  {conversations.map(conversation => (
                    <option key={conversation.id} value={conversation.id}>
                      {conversation.name}
                    </option>
                  ))}
                </select>
                <p className="text-blue-400/70 text-xs mt-1">
                  Se selecionado, esta memória só será usada no chat especificado
                </p>
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setMemoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        memoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="memory-active"
                  checked={memoryForm.isActive}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="memory-active" className="text-sm text-blue-300">
                  Memória ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowMemoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingMemory ? handleUpdateMemory : handleCreateMemory}
                disabled={!memoryForm.title.trim() || !memoryForm.content.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingMemory ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Categoria */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-blue-950 border border-blue-700/30 rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-white">
                {editingCategory ? 'Editar Categoria' : 'Nova Categoria'}
              </h3>
              <button
                onClick={() => setShowCategoryModal(false)}
                className="text-blue-300 hover:text-blue-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Nome *
                </label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências pessoais"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Descrição
                </label>
                <textarea
                  value={categoryForm.description}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Descrição opcional da categoria"
                />
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setCategoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        categoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="category-active"
                  checked={categoryForm.isActive}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="category-active" className="text-sm text-blue-300">
                  Categoria ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowCategoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingCategory ? handleUpdateCategory : handleCreateCategory}
                disabled={!categoryForm.name.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingCategory ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filtros de categoria melhorados */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
          </svg>
          <h4 className="text-lg font-semibold text-white">Filtrar por Categoria</h4>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
              selectedCategory === 'all'
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg shadow-blue-500/25'
                : 'bg-blue-900/20 text-blue-300 hover:bg-blue-800/30 border border-blue-700/30 hover:border-blue-500/50'
            }`}
          >
            {selectedCategory === 'all' && (
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur-lg"></div>
            )}
            <span className="relative flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              Todas ({memories.length})
            </span>
          </button>

          <button
            onClick={() => setSelectedCategory('uncategorized')}
            className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
              selectedCategory === 'uncategorized'
                ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg shadow-gray-500/25'
                : 'bg-gray-900/20 text-gray-300 hover:bg-gray-800/30 border border-gray-700/30 hover:border-gray-500/50'
            }`}
          >
            {selectedCategory === 'uncategorized' && (
              <div className="absolute inset-0 bg-gradient-to-r from-gray-400/20 to-gray-600/20 rounded-xl blur-lg"></div>
            )}
            <span className="relative flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Sem categoria ({memories.filter(m => !m.categoryId).length})
            </span>
          </button>

          {categories.map(category => {
            const categoryMemories = memories.filter(m => m.categoryId === category.id);
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                  selectedCategory === category.id
                    ? 'text-white shadow-lg'
                    : 'bg-blue-900/20 text-blue-300 hover:bg-blue-800/30 border border-blue-700/30 hover:border-blue-500/50'
                }`}
                style={selectedCategory === category.id ? {
                  background: `linear-gradient(135deg, ${category.backgroundColor}80, ${category.backgroundColor}60)`
                } : {}}
              >
                {selectedCategory === category.id && (
                  <div
                    className="absolute inset-0 rounded-xl blur-lg opacity-30"
                    style={{ background: `linear-gradient(135deg, ${category.backgroundColor}, ${category.backgroundColor}80)` }}
                  ></div>
                )}
                <span className="relative flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full shadow-sm"
                    style={{ backgroundColor: category.backgroundColor }}
                  />
                  {category.name} ({categoryMemories.length})
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Lista de categorias melhorada */}
      {categories.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h4 className="text-xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
              Categorias ({categories.length})
            </h4>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {categories.map(category => {
              const categoryMemories = memories.filter(m => m.categoryId === category.id);
              const activeMemories = categoryMemories.filter(m => m.isActive);

              return (
                <div
                  key={category.id}
                  className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl"
                  style={{
                    background: `linear-gradient(135deg, ${category.backgroundColor}15, ${category.backgroundColor}08)`,
                    borderColor: `${category.backgroundColor}40`
                  }}
                >
                  {/* Glow effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl"
                    style={{ background: `linear-gradient(135deg, ${category.backgroundColor}, ${category.backgroundColor}80)` }}
                  ></div>

                  {/* Border gradient */}
                  <div
                    className="absolute inset-0 rounded-2xl border opacity-30 group-hover:opacity-60 transition-opacity duration-300"
                    style={{ borderColor: category.backgroundColor }}
                  ></div>

                  <div className="relative p-6 space-y-4">
                    {/* Header da categoria */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-4">
                        <div
                          className="w-12 h-12 rounded-xl shadow-lg flex items-center justify-center"
                          style={{ backgroundColor: `${category.backgroundColor}20`, border: `1px solid ${category.backgroundColor}40` }}
                        >
                          <div
                            className="w-6 h-6 rounded-lg shadow-sm"
                            style={{ backgroundColor: category.backgroundColor }}
                          />
                        </div>
                        <div>
                          <h5 className="text-lg font-bold text-white mb-1">{category.name}</h5>
                          {category.description && (
                            <p className="text-blue-300/80 text-sm leading-relaxed">{category.description}</p>
                          )}
                        </div>
                      </div>

                      {/* Status indicator */}
                      <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                        category.isActive
                          ? 'bg-green-500/20 text-green-300 border-green-400/30'
                          : 'bg-gray-500/20 text-gray-300 border-gray-400/30'
                      }`}>
                        {category.isActive ? 'Ativa' : 'Inativa'}
                      </div>
                    </div>

                    {/* Estatísticas da categoria */}
                    <div className="flex gap-4 text-sm">
                      <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-500/10 border border-blue-400/20 rounded-lg">
                        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="text-blue-300 font-medium">{categoryMemories.length} memórias</span>
                      </div>
                      <div className="flex items-center gap-2 px-3 py-1.5 bg-green-500/10 border border-green-400/20 rounded-lg">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-green-300 font-medium">{activeMemories.length} ativas</span>
                      </div>
                    </div>

                    {/* Botões de ação */}
                    <div className="flex items-center justify-between pt-2">
                      <button
                        onClick={() => handleToggleCategoryMemories(category.id, !category.isActive)}
                        className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                          category.isActive
                            ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white shadow-lg shadow-green-500/25'
                            : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white shadow-lg shadow-gray-500/25'
                        }`}
                      >
                        {category.isActive ? 'Desativar' : 'Ativar'}
                      </button>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => openCategoryModal(category)}
                          className="p-2.5 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-xl transition-all duration-200 hover:scale-110"
                          title="Editar categoria"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className="p-2.5 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-xl transition-all duration-200 hover:scale-110"
                          title="Deletar categoria"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Lista de memórias melhorada */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h4 className="text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Memórias
            </h4>
            {selectedCategory !== 'all' && (
              <span className="px-3 py-1 bg-blue-500/20 text-blue-300 text-sm font-medium rounded-full border border-blue-400/30">
                {filteredMemories.length} encontradas
              </span>
            )}
          </div>
        </div>

        {filteredMemories.length === 0 ? (
          <div className="relative">
            {/* Background com gradiente */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-indigo-500/5 rounded-2xl backdrop-blur-sm"></div>

            <div className="relative text-center py-16 px-8">
              <div className="mb-6">
                <div className="relative inline-block">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
                  <div className="relative p-6 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full border border-blue-400/20">
                    <svg className="w-16 h-16 text-blue-400/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
              </div>

              <h3 className="text-xl font-bold text-white mb-3">
                {selectedCategory === 'all' ? 'Nenhuma memória criada ainda' : 'Nenhuma memória nesta categoria'}
              </h3>
              <p className="text-blue-300/70 text-sm mb-6 max-w-md mx-auto leading-relaxed">
                {selectedCategory === 'all'
                  ? 'Crie sua primeira memória para personalizar as respostas da IA com informações importantes sobre você.'
                  : 'Esta categoria ainda não possui memórias. Crie uma nova memória ou mude o filtro.'
                }
              </p>

              <button
                onClick={() => openMemoryModal()}
                className="group relative px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                <span className="relative flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Criar primeira memória
                </span>
              </button>
            </div>
          </div>
        ) : (
          <div className="grid gap-6">
            {filteredMemories.map(memory => {
              const category = categories.find(c => c.id === memory.categoryId);
              const chat = conversations.find(c => c.id === memory.chatId);

              return (
                <div
                  key={memory.id}
                  className="group relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-[1.01] hover:shadow-2xl"
                  style={{
                    background: `linear-gradient(135deg, ${memory.backgroundColor}12, ${memory.backgroundColor}06)`,
                    borderColor: `${memory.backgroundColor}40`
                  }}
                >
                  {/* Glow effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-15 transition-opacity duration-300 blur-xl"
                    style={{ background: `linear-gradient(135deg, ${memory.backgroundColor}, ${memory.backgroundColor}80)` }}
                  ></div>

                  {/* Border gradient */}
                  <div
                    className="absolute inset-0 rounded-2xl border opacity-40 group-hover:opacity-70 transition-opacity duration-300"
                    style={{ borderColor: memory.backgroundColor }}
                  ></div>

                  <div className="relative p-6">
                    <div className="flex justify-between items-start gap-4">
                      <div className="flex-1 space-y-4">
                        {/* Header da memória */}
                        <div className="flex items-start gap-4">
                          <div
                            className="w-10 h-10 rounded-xl shadow-lg flex items-center justify-center flex-shrink-0 mt-1"
                            style={{ backgroundColor: `${memory.backgroundColor}25`, border: `1px solid ${memory.backgroundColor}50` }}
                          >
                            <div
                              className="w-5 h-5 rounded-lg shadow-sm"
                              style={{ backgroundColor: memory.backgroundColor }}
                            />
                          </div>

                          <div className="flex-1 min-w-0">
                            <h5 className="text-lg font-bold text-white mb-2 leading-tight">{memory.title}</h5>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-2 mb-3">
                              {memory.chatId && chat && (
                                <span className="inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-purple-500/20 to-purple-600/20 text-purple-300 text-xs font-medium rounded-full border border-purple-400/30">
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                  </svg>
                                  {chat.name}
                                </span>
                              )}
                              {memory.chatId && !chat && (
                                <span className="inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r from-red-500/20 to-red-600/20 text-red-300 text-xs font-medium rounded-full border border-red-400/30">
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                  </svg>
                                  Chat não encontrado
                                </span>
                              )}
                              {category && (
                                <span
                                  className="inline-flex items-center gap-1.5 px-3 py-1 text-xs font-medium rounded-full border"
                                  style={{
                                    background: `linear-gradient(135deg, ${category.backgroundColor}20, ${category.backgroundColor}10)`,
                                    color: category.backgroundColor,
                                    borderColor: `${category.backgroundColor}40`
                                  }}
                                >
                                  <div
                                    className="w-2 h-2 rounded-full"
                                    style={{ backgroundColor: category.backgroundColor }}
                                  />
                                  {category.name}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Conteúdo da memória */}
                        <div className="bg-blue-950/30 border border-blue-700/20 rounded-xl p-4 backdrop-blur-sm">
                          <p className="text-blue-100/90 text-sm leading-relaxed">{memory.content}</p>
                        </div>

                        {/* Metadados */}
                        <div className="flex items-center gap-4 text-xs text-blue-300/60">
                          <div className="flex items-center gap-1.5">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Criado em {new Date(memory.createdAt).toLocaleDateString('pt-BR')}
                          </div>
                          {memory.updatedAt && memory.updatedAt !== memory.createdAt && (
                            <div className="flex items-center gap-1.5">
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                              Editado em {new Date(memory.updatedAt).toLocaleDateString('pt-BR')}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Botões de ação */}
                      <div className="flex flex-col gap-3 flex-shrink-0">
                        <button
                          onClick={() => memoryService.updateMemory(user!.uid, memory.id, { isActive: !memory.isActive }).then(loadData)}
                          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                            memory.isActive
                              ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white shadow-lg shadow-green-500/25'
                              : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white shadow-lg shadow-gray-500/25'
                          }`}
                        >
                          {memory.isActive ? 'Ativa' : 'Inativa'}
                        </button>

                        <div className="flex gap-2">
                          <button
                            onClick={() => openMemoryModal(memory)}
                            className="p-2.5 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-xl transition-all duration-200 hover:scale-110"
                            title="Editar memória"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteMemory(memory.id)}
                            className="p-2.5 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-xl transition-all duration-200 hover:scale-110"
                            title="Deletar memória"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
